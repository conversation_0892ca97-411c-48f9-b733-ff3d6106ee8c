import { fetchEventSource, EventSourceMessage } from '@microsoft/fetch-event-source';
import { mockTree } from './craftingTree';

interface ConversationSSECallbacks {
  onOpen?: (response: Response) => void;
  onMessageChunk?: (chunk: string) => void;
  onEnd?: () => void;
  onError?: (msg: string) => void;
  onConversationStart?: (data: { conversationId: number; type: 'NORMAL' | 'SYNTHETIC_NAVIGATION'; title: string }) => void;
  onTitleUpdate?: (title: string) => void;
}

// Mock switch for SSE
const USE_SSE_MOCK = true;

// 新建会话的SSE流
export function startNewConversationSSE({
  message,
  type = 'NORMAL',
  token,
  callbacks
}: {
  message: string;
  type?: 'NORMAL' | 'SYNTHETIC_NAVIGATION';
  token: string;
  callbacks: ConversationSSECallbacks & {
    onCraftingContext?: (ctx: any) => void;
    onMessageComplete?: (data: any) => void;
  };
}): AbortController {
  const ctrl = new AbortController();
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Authorization': `Bearer ${token}`
  };
  const requestBody = JSON.stringify({ message, type });
  const endpointUrl = `/api/conversations/new`;

  console.log('=== 发起SSE请求 ===');
  console.log('URL:', endpointUrl);
  console.log('Headers:', headers);
  console.log('Body:', requestBody);
  console.log('=== SSE请求信息结束 ===');

  fetchEventSource(endpointUrl, {
    method: 'POST',
    headers,
    body: requestBody,
    signal: ctrl.signal,
    openWhenHidden: true,
    // 添加自定义fetch函数来调试网络层
    fetch: async (input, init) => {
      console.log('=== 自定义fetch调用 ===');
      console.log('Input:', input);
      console.log('Init:', init);
      const response = await fetch(input, init);
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      console.log('=== 自定义fetch结束 ===');
      return response;
    },
    onopen: async (response) => {
      if (response.ok) {
        callbacks.onOpen?.(response);
      } else {
        const errorText = await response.text();
        callbacks.onError?.(`连接错误: ${response.status}`);
        ctrl.abort();
      }
    },
    onmessage: (event: EventSourceMessage) => {
      // 添加原始事件数据的详细日志
      console.log('=== 原始SSE事件 ===');
      console.log('event.event:', JSON.stringify(event.event));
      console.log('event.data (原始):', JSON.stringify(event.data));
      console.log('event.data (长度):', event.data ? event.data.length : 'null');

      // 检查是否包含换行符
      if (event.data && event.data.includes('\n')) {
        console.log('⚠️ 警告：event.data包含换行符！');
        console.log('换行符位置:', event.data.split('').map((c, i) => c === '\n' ? i : null).filter(i => i !== null));
      }

      // 检查是否是不完整的JSON
      if (event.data && event.data.includes('{') && !event.data.includes('}')) {
        console.log('⚠️ 警告：event.data可能是不完整的JSON！');
      }

      console.log('=== 原始SSE事件结束 ===');

      if (event.event === 'end') {
        callbacks.onEnd?.();
        ctrl.abort();
        return;
      }
      if (event.event === 'error') {
        try {
          const errorData = JSON.parse(event.data);
          callbacks.onError?.(`抱歉，处理时发生错误：${errorData.message}`);
        } catch (e) {
          callbacks.onError?.('抱歉，处理时发生未知错误。');
        }
        ctrl.abort();
        return;
      }
      switch (event.event) {
        case 'conversation_start': {
          try {
            const data = JSON.parse(event.data);
            // API sends conversationId, type, title
            callbacks.onConversationStart?.({
              conversationId: data.conversationId,
              type: data.type,
              title: data.title
            });
          } catch {}
          break;
        }
        case 'crafting_context': {
          try {
            const ctx = JSON.parse(event.data);
            callbacks.onCraftingContext?.(ctx);
          } catch (e) {}
          break;
        }
        case 'message': {
          console.log('=== SSE接收到message事件 ===');
          console.log('原始event.data:', event.data);
          // try {
          //   const msg = JSON.parse(event.data);
          //   console.log('解析后的msg:', msg);
          //   console.log('msg.chunk内容:', msg.chunk);
          //   callbacks.onMessageChunk?.(msg.chunk);
          // } catch {
          //   console.log('JSON解析失败，直接使用event.data:', event.data);
          callbacks.onMessageChunk?.(event.data);
          // }
          break;
        }
        case 'message_complete': {
          try {
            const completeData = JSON.parse(event.data);
            callbacks.onMessageComplete?.({
              messageId: completeData.messageId,
              type: completeData.type,
              item_name: completeData.item_name
            });
          } catch {}
          break;
        }
        default:
          // 修复：避免重复处理已经在case分支中处理过的事件
          if (event.data && event.event !== 'message' && event.event !== 'message_complete' && event.event !== 'crafting_context') {
            console.log('=== SSE default分支处理未知事件 ===');
            console.log('event.event:', event.event);
            console.log('event.data:', event.data);
            try {
              const msg = JSON.parse(event.data);
              console.log('解析后的msg:', msg);
              // 修复：明确处理chunk，即使是空字符串也要传递
              if (msg.hasOwnProperty('chunk')) {
                console.log('使用msg.chunk:', JSON.stringify(msg.chunk));
                callbacks.onMessageChunk?.(msg.chunk);
              } else {
                console.log('没有chunk属性，使用整个event.data:', event.data);
                callbacks.onMessageChunk?.(event.data);
              }
            } catch {
              console.log('JSON解析失败，直接使用event.data:', event.data);
              callbacks.onMessageChunk?.(event.data);
            }
          } else if (event.data && !event.event) {
            // 处理没有event字段的数据
            console.log('=== SSE处理无event字段的数据 ===');
            console.log('event.data:', event.data);
            try {
              const msg = JSON.parse(event.data);
              console.log('解析后的msg:', msg);
              if (msg.hasOwnProperty('chunk')) {
                console.log('使用msg.chunk:', JSON.stringify(msg.chunk));
                callbacks.onMessageChunk?.(msg.chunk);
              } else {
                console.log('没有chunk属性，使用整个event.data:', event.data);
                callbacks.onMessageChunk?.(event.data);
              }
            } catch {
              console.log('JSON解析失败，直接使用event.data:', event.data);
              callbacks.onMessageChunk?.(event.data);
            }
          }
      }
    },
    onclose: () => {
      callbacks.onEnd?.();
    },
    onerror: (err) => {
      callbacks.onError?.('网络连接错误或服务器无响应。');
      throw err;
    }
  });
  return ctrl;
}

// 追加消息到已有会话的SSE流
export function addMessageToConversationSSE({
  conversationId,
  message,
  type,
  token,
  callbacks
}: {
  conversationId: string;
  message: string;
  type?: string;
  token: string;
  callbacks: ConversationSSECallbacks & {
    // onCraftingContext?: (ctx: any) => void; // <-- Remove this callback option
    onMessageComplete?: (data: any) => void;
  };
}): AbortController {
  const ctrl = new AbortController();
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Authorization': `Bearer ${token}`
  };
  const requestBody = JSON.stringify({ content: message }); // 修复：字段名改为 content
  const endpointUrl = `/api/conversations/${conversationId}/messages`;

  console.log('=== 发起SSE请求(addMessage) ===');
  console.log('URL:', endpointUrl);
  console.log('Headers:', headers);
  console.log('Body:', requestBody);
  console.log('=== SSE请求信息结束(addMessage) ===');

  fetchEventSource(endpointUrl, {
    method: 'POST',
    headers,
    body: requestBody,
    signal: ctrl.signal,
    openWhenHidden: true,
    // 添加自定义fetch函数来调试网络层
    fetch: async (input, init) => {
      console.log('=== 自定义fetch调用(addMessage) ===');
      console.log('Input:', input);
      console.log('Init:', init);
      const response = await fetch(input, init);
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      console.log('=== 自定义fetch结束(addMessage) ===');
      return response;
    },
    onopen: async (response) => {
      if (response.ok) {
        callbacks.onOpen?.(response);
      } else {
        const errorText = await response.text();
        callbacks.onError?.(`连接错误: ${response.status}`);
        ctrl.abort();
      }
    },
    onmessage: (event: EventSourceMessage) => {
      // 添加原始事件数据的详细日志
      console.log('=== 原始SSE事件(addMessage) ===');
      console.log('event.event:', JSON.stringify(event.event));
      console.log('event.data (原始):', JSON.stringify(event.data));
      console.log('event.data (长度):', event.data ? event.data.length : 'null');
      console.log('event.data (字符码):', event.data ? Array.from(event.data).map(c => c.charCodeAt(0)) : 'null');
      console.log('=== 原始SSE事件结束(addMessage) ===');

      if (event.event === 'end') {
        callbacks.onEnd?.();
        ctrl.abort();
        return;
      }
      if (event.event === 'error') {
        try {
          const errorData = JSON.parse(event.data);
          callbacks.onError?.(`抱歉，处理时发生错误：${errorData.message}`);
        } catch (e) {
          callbacks.onError?.('抱歉，处理时发生未知错误。');
        }
        ctrl.abort();
        return;
      }
      switch (event.event) {
        // --- Remove case for crafting_context ---
        // case 'crafting_context': {
        //   try {
        //     const ctx = JSON.parse(event.data);
        //     callbacks.onCraftingContext?.(ctx);
        //   } catch (e) {}
        //   break;
        // }
        // --- End removed case ---
        case 'message': {
          console.log('=== SSE接收到message事件(addMessage) ===');
          console.log('原始event.data:', event.data);
          // try {
          //   const msg = JSON.parse(event.data);
          //   console.log('解析后的msg:', msg);
          //   console.log('msg.chunk内容:', msg.chunk);
          //   callbacks.onMessageChunk?.(msg.chunk);
          // } catch {
          //   console.log('JSON解析失败，直接使用event.data:', event.data);
            callbacks.onMessageChunk?.(event.data);
          }
          break;
        }
        case 'message_complete': {
          try {
            const completeData = JSON.parse(event.data);
            callbacks.onMessageComplete?.({
              messageId: completeData.messageId,
              type: completeData.type,
              item_name: completeData.item_name
            });
          } catch {}
          break;
        }
        default:
          // 修复：避免重复处理已经在case分支中处理过的事件
          if (event.data && event.event !== 'message' && event.event !== 'message_complete') {
            console.log('=== SSE default分支处理未知事件(addMessage) ===');
            console.log('event.event:', event.event);
            console.log('event.data:', event.data);
            try {
              const msg = JSON.parse(event.data);
              console.log('解析后的msg:', msg);
              // 修复：明确处理chunk，即使是空字符串也要传递
              if (msg.hasOwnProperty('chunk')) {
                console.log('使用msg.chunk:', JSON.stringify(msg.chunk));
                callbacks.onMessageChunk?.(msg.chunk);
              } else {
                console.log('没有chunk属性，使用整个event.data:', event.data);
                callbacks.onMessageChunk?.(event.data);
              }
            } catch {
              console.log('JSON解析失败，直接使用event.data:', event.data);
              callbacks.onMessageChunk?.(event.data);
            }
          } else if (event.data && !event.event) {
            // 处理没有event字段的数据
            console.log('=== SSE处理无event字段的数据(addMessage) ===');
            console.log('event.data:', event.data);
            try {
              const msg = JSON.parse(event.data);
              console.log('解析后的msg:', msg);
              if (msg.hasOwnProperty('chunk')) {
                console.log('使用msg.chunk:', JSON.stringify(msg.chunk));
                callbacks.onMessageChunk?.(msg.chunk);
              } else {
                console.log('没有chunk属性，使用整个event.data:', event.data);
                callbacks.onMessageChunk?.(event.data);
              }
            } catch {
              console.log('JSON解析失败，直接使用event.data:', event.data);
              callbacks.onMessageChunk?.(event.data);
            }
          }
      }
    },
    onclose: () => {
      callbacks.onEnd?.();
    },
    onerror: (err) => {
      callbacks.onError?.('网络连接错误或服务器无响应。');
      throw err;
    }
  });
  return ctrl;
}
